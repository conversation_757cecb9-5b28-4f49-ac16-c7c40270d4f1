@extends('backend.layouts.app')
@section('title')
    {{ __('company_list') }}
@endsection

@section('content')
    <div class="container-fluid">
        <!-- Statistik Card -->
        <div class="row mb-3">
            <div class="col-md-3 col-sm-6 col-12">
                <div class="info-box bg-info stat-card" data-url="{{ route('company.index') }}" data-filter="all">
                    <div class="info-box-content">
                        <span class="info-box-text">Total Perusahaan</span>
                        <span class="info-box-number">{{ $totalCompanies }}</span>
                        {{-- <div class="progress">
                            <div class="progress-bar" style="width: 100%"></div>
                        </div> --}}
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-sm-6 col-12">
                <div class="info-box bg-success stat-card" data-url="{{ route('company.index', ['status' => 'active']) }}" data-filter="active">
                    <div class="info-box-content">
                        <span class="info-box-text">Perusahaan Aktif</span>
                        <span class="info-box-number">{{ $activeCompanies }}</span>
                        {{-- <div class="progress">
                            <div class="progress-bar" style="width: {{ $totalCompanies > 0 ? ($activeCompanies / $totalCompanies) * 100 : 0 }}%"></div>
                        </div> --}}
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-sm-6 col-12">
                <div class="info-box bg-danger stat-card" data-url="{{ route('company.index', ['status' => 'inactive']) }}" data-filter="inactive">
                    <div class="info-box-content">
                        <span class="info-box-text">Perusahaan Tidak Aktif</span>
                        <span class="info-box-number">{{ $inactiveCompanies }}</span>
                        {{-- <div class="progress">
                            <div class="progress-bar" style="width: {{ $totalCompanies > 0 ? ($inactiveCompanies / $totalCompanies) * 100 : 0 }}%"></div>
                        </div> --}}
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-sm-6 col-12">
                <div class="info-box bg-warning stat-card" data-url="{{ route('company.index', ['has_jobs' => 'true']) }}" data-filter="has_jobs">
                    <div class="info-box-content">
                        <span class="info-box-text">Perusahaan Dengan Loker</span>
                        <span class="info-box-number">{{ $companiesWithJobs }}</span>
                        {{-- <div class="progress">
                            <div class="progress-bar" style="width: {{ $totalCompanies > 0 ? ($companiesWithJobs / $totalCompanies) * 100 : 0 }}%"></div>
                        </div> --}}
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h3 class="card-title line-height-36">{{ __('company_list') }}</h3>
                            <div>
                                <a href="{{ route('company.index', ['view' => 'old']) }}" class="btn bg-info mr-1">
                                    <i class="fas fa-list"></i>&nbsp; {{ __('Tampilan Lama') }}
                                </a>
                                @if (userCan('company.create'))
                                    <a href="{{ route('company.create') }}" class="btn bg-primary">
                                        <i class="fas fa-plus mr-1"></i> {{ __('Tambah Perusahaan') }}
                                    </a>
                                @endif
                                @if (request('keyword') || request('organization_type') || request('industry_type') || request('sort_by'))
                                    <a href="{{ route('company.index') }}" class="btn bg-danger">
                                        <i class="fas fa-times"></i>&nbsp; {{ __('Hapus Filter') }}
                                    </a>
                                @endif
                            </div>
                        </div>
                    </div>

                    <!-- Filter Section -->
                    <form id="filterForm" action="{{ route('company.index') }}" method="GET" onsubmit="return applyFilter(this);">
                        <input type="hidden" name="view" value="new">
                        @if(request('has_jobs'))
                        <input type="hidden" name="has_jobs" value="{{ request('has_jobs') }}">
                        @endif
                        @if(request('status'))
                        <input type="hidden" name="status" value="{{ request('status') }}">
                        @endif
                        <div class="card-body border-bottom row">
                            <div class="col-12 col-md-3 mb-3">
                                <label>{{ __('search') }}</label>
                                <input name="keyword" type="text" placeholder="{{ __('search') }}" class="form-control"
                                    value="{{ request('keyword') }}">
                            </div>
                            <div class="col-12 col-md-3 mb-3">
                                <label>{{ __('organization_type') }}</label>
                                <select name="organization_type" class="form-control select2bs4">
                                    <option value="">
                                        {{ __('all') }}
                                    </option>
                                    @foreach ($organization_types as $organization_type)
                                        @if($organization_type->companies_count > 0)
                                            <option {{ request('organization_type') == $organization_type->id ? 'selected' : '' }}
                                                value="{{ $organization_type->id }}">
                                                {{ $organization_type->name }} ({{ $organization_type->companies_count }})
                                            </option>
                                        @endif
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-12 col-md-3 mb-3">
                                <label>{{ __('industry_type') }}</label>
                                <select name="industry_type" class="form-control select2bs4">
                                    <option value="">
                                        {{ __('all') }}
                                    </option>
                                    @foreach ($industry_types as $industry_type)
                                        @if($industry_type->companies_count > 0)
                                            <option {{ request('industry_type') == $industry_type->id ? 'selected' : '' }}
                                                value="{{ $industry_type->id }}">
                                                {{ $industry_type->name }} ({{ $industry_type->companies_count }})
                                            </option>
                                        @endif
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-12 col-md-3 mb-3">
                                <label>{{ __('sort_by') }}</label>
                                <select name="sort_by" class="form-control select2bs4">
                                    <option {{ !request('sort_by') || request('sort_by') == 'latest' ? 'selected' : '' }}
                                        value="latest" selected>
                                        {{ __('latest') }}
                                    </option>
                                    <option {{ request('sort_by') == 'oldest' ? 'selected' : '' }} value="oldest">
                                        {{ __('oldest') }}
                                    </option>
                                </select>
                            </div>
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary" id="filterButton">
                                    <i class="fas fa-filter"></i> {{ __('Filter') }}
                                </button>
                                <button type="button" class="btn btn-secondary ml-1" id="resetFilterButton">
                                    <i class="fas fa-undo"></i> {{ __('Reset') }}
                                </button>
                            </div>
                        </div>
                    </form>

                    <div class="card-body table-responsive p-0">
                        @include('backend.layouts.partials.message')
                        <div id="companiesTableContainer">
                            <table id="companiesTable" class="table table-hover text-nowrap table-bordered">
                            <thead>
                                <tr>
                                    <th>{{ __('company') }}</th>
                                    <th>{{ __('active') }} {{ __('job') }}</th>
                                    <th>{{ __('Badan Hukum') }}/{{ __('Kab/Kota') }}</th>
                                    <th>{{ __('Jenis Industri') }}</th>
                                    <th>{{ __('Status') }}</th>
                                    <th>{{ __('action') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse ($companies as $company)
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <img src="{{ $company->logo_url }}" alt="Logo" class="mr-2 company-logo">
                                                <div>
                                                    <h5 class="m-0" data-toggle="tooltip" title="{{ formatCompanyName($company) }}">
                                                        {{ \Illuminate\Support\Str::limit(formatCompanyName($company), 20, '...') }}
                                                        @if ($company->is_profile_verified)
                                                            <svg style="width: 20px; height: 20px; color: green"
                                                                xmlns="http://www.w3.org/2000/svg" fill="none"
                                                                viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                                <path stroke-linecap="round" stroke-linejoin="round"
                                                                    d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                            </svg>
                                                        @endif
                                                    </h5>
                                                    <p class="text-muted m-0">{{ $company->user->email }}</p>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <button type="button" class="btn btn-info btn-sm view-jobs"
                                                data-toggle="modal" data-target="#jobsModal"
                                                data-company-id="{{ $company->id }}"
                                                data-company-name="{{ formatCompanyName($company) }}">
                                                {{ $company->active_jobs }} {{ __('active_jobs') }}
                                            </button>
                                        </td>
                                        <td>
                                            <p class="m-0"><strong>{{ $company->organization->name }}</strong></p>
                                            <p class="m-0">{{ $company->district }}</p>
                                        </td>
                                        <td>
                                            {{ $company->industry->name }}
                                        </td>
                                        <td>
                                            <div class="d-flex flex-column">
                                                <button type="button" class="btn btn-sm mb-1 view-document"
                                                    data-toggle="modal" data-target="#documentModal"
                                                    data-company-id="{{ $company->id }}"
                                                    data-company-name="{{ formatCompanyName($company) }}"
                                                    data-document-url="{{ $company->user->ak1 ? url('storage/' . $company->user->ak1) : '' }}"
                                                    data-document-type="{{ $company->user->ak1 ? strtolower(pathinfo($company->user->ak1, PATHINFO_EXTENSION)) : '' }}"
                                                    data-status="{{ $company->user->status }}"
                                                    data-user-id="{{ $company->user_id }}">
                                                    <i class="fas fa-file-alt"></i> {{ __('Lihat Dokumen') }}
                                                </button>

                                                @if (userCan('company.update'))
                                                    <div class="d-flex align-items-center">
                                                        <label class="switch mr-2">
                                                            <input data-id="{{ $company->user_id }}" type="checkbox"
                                                                class="success status-switch"
                                                                {{ $company->user->status == 1 ? 'checked' : '' }}>
                                                            <span class="slider round"></span>
                                                        </label>
                                                        <span class="badge {{ $company->user->status == 1 ? 'badge-success' : 'badge-danger' }}"
                                                            id="status_badge_{{ $company->user_id }}">
                                                            {{ $company->user->status == 1 ? __('activated') : __('deactivated') }}
                                                        </span>
                                                    </div>
                                                @else
                                                    <span class="badge {{ $company->user->status == 1 ? 'badge-success' : 'badge-danger' }}">
                                                        {{ $company->user->status == 1 ? __('activated') : __('deactivated') }}
                                                    </span>
                                                @endif
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex">
                                                @if (userCan('company.view'))
                                                    <a href="{{ route('company.show', $company->id) }}"
                                                        class="btn btn-info btn-sm mr-1"
                                                        data-toggle="tooltip"
                                                        title="{{ __('view_profile') }}">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                @endif
                                                @if (userCan('company.update'))
                                                    <a href="{{ route('company.edit', $company->id) }}"
                                                        class="btn btn-primary btn-sm mr-1"
                                                        data-toggle="tooltip"
                                                        title="{{ __('edit') }}">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                @endif
                                                @if (userCan('company.delete'))
                                                    <button type="button"
                                                        class="btn btn-danger btn-sm delete-btn"
                                                        data-toggle="tooltip"
                                                        title="{{ __('delete') }}"
                                                        data-company-id="{{ $company->id }}"
                                                        data-company-name="{{ formatCompanyName($company) }}">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="6" class="text-center">
                                            <p>{{ __('no_data_found') }}...</p>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                        @if ($companies->count())
                            <div class="mt-3 d-flex justify-content-center" id="paginationContainer">
                                {{ $companies->appends(request()->all())->links() }}
                            </div>
                        @endif
                        </div><!-- End of companiesTableContainer -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Document Modal -->
    <div class="modal fade" id="documentModal" tabindex="-1" role="dialog" aria-labelledby="documentModalLabel">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="documentModalLabel">Dokumen Perusahaan</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <h4 class="text-center mb-3" id="companyNameInModal"></h4>
                    <div id="documentViewer" class="text-center mb-3">
                        <!-- Document will be displayed here -->
                    </div>
                    <div class="text-center mb-3">
                        <a href="#" id="downloadDocument" class="btn btn-success" target="_blank">
                            <i class="fas fa-download"></i> Unduh Dokumen
                        </a>
                    </div>
                    <div class="text-center">
                        <div class="alert alert-warning mb-3">
                            <strong>Perhatian!</strong> Mengubah status perusahaan akan mempengaruhi akses perusahaan ke sistem.
                        </div>
                        <button type="button" id="toggleStatusBtn" class="btn btn-lg btn-success">
                            <i class="fas fa-power-off mr-1"></i> Aktifkan
                        </button>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Tutup</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Jobs Modal -->
    <div class="modal fade" id="jobsModal" tabindex="-1" role="dialog" aria-labelledby="jobsModalLabel">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="jobsModalLabel">Daftar Lowongan Kerja {{ $company->name ?? '' }}</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <h4 class="text-center mb-3" id="companyNameInJobsModal"></h4>
                    <div id="jobsList">
                        <!-- Jobs will be displayed here -->
                        <div class="text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="sr-only">Loading...</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Tutup</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteModalLabel">Konfirmasi Hapus</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <p>Apakah Anda yakin ingin menghapus perusahaan <span id="companyNameInDeleteModal" class="font-weight-bold"></span>?</p>
                    <p class="text-danger">Tindakan ini tidak dapat dibatalkan!</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                    <form id="deleteForm" action="" method="POST">
                        @method('DELETE')
                        @csrf
                        <button type="submit" class="btn btn-danger">Hapus</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('style')
    <link rel="stylesheet" href="{{ asset('backend') }}/plugins/datatables-bs4/css/dataTables.bootstrap4.min.css">
    <link rel="stylesheet" href="{{ asset('backend') }}/plugins/datatables-responsive/css/responsive.bootstrap4.min.css">
    <style>
        .company-logo {
            width: 50px;
            height: 50px;
            object-fit: cover;
            border-radius: 50%;
        }

        @media (max-width: 767px) {
            .table-responsive {
                overflow-x: auto;
            }

            #companiesTable {
                width: 100%;
            }

            #companiesTable th,
            #companiesTable td {
                white-space: nowrap;
            }

            .company-logo {
                width: 40px;
                height: 40px;
            }

            .d-flex.align-items-center {
                flex-direction: column;
                align-items: flex-start !important;
            }

            .d-flex.align-items-center img {
                margin-bottom: 5px;
            }
        }

        /* PDF viewer styling */
        .pdf-container {
            width: 100%;
            height: 500px;
            overflow: hidden;
            margin-bottom: 20px;
        }

        .pdf-container iframe {
            width: 100%;
            height: 100%;
            border: none;
        }

        /* Image viewer styling */
        .img-container {
            width: 100%;
            text-align: center;
            margin-bottom: 20px;
        }

        .img-container img {
            max-width: 100%;
            max-height: 500px;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 5px;
        }

        /* Statistik card styling */
        .info-box {
            box-shadow: 0 0 1px rgba(0,0,0,.125), 0 1px 3px rgba(0,0,0,.2);
            border-radius: 0.25rem;
            padding: 0;
            min-height: 100px;
            position: relative;
            transition: all .3s;
        }

        .info-box:hover {
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,.3);
            cursor: pointer;
        }

        .info-box-icon {
            border-radius: 0.25rem 0 0 0.25rem;
            display: block;
            width: 80px;
            text-align: center;
            font-size: 30px;
            line-height: 100px;
            float: left;
        }

        .info-box-content {
            padding: 5px 10px;
            text-align: center;
            width: 100%;
        }

        .info-box-text {
            display: block;
            font-size: 14px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            text-transform: uppercase;
        }

        .info-box-number {
            display: block;
            font-weight: 700;
            font-size: 24px;
        }

        .info-box .progress {
            background-color: rgba(0,0,0,.125);
            height: 2px;
            margin: 5px 0;
        }

        .info-box .progress .progress-bar {
            background-color: #fff;
        }

        .small-box-footer {
            color: rgba(255,255,255,.8);
            display: block;
            padding: 3px 0;
            position: relative;
            text-align: center;
            text-decoration: none;
            z-index: 10;
        }

        .small-box-footer:hover {
            color: #fff;
            text-decoration: none;
        }

        .bg-info .info-box-icon, .bg-success .info-box-icon, .bg-warning .info-box-icon, .bg-danger .info-box-icon {
            color: rgba(255,255,255,.8);
        }

        .active-card {
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,.5) !important;
            border: 2px solid #fff;
        }

        /* Switch styling */
        .switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            -webkit-transition: .4s;
            transition: .4s;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            -webkit-transition: .4s;
            transition: .4s;
        }

        input:checked + .slider {
            background-color: #28a745;
        }

        input:focus + .slider {
            box-shadow: 0 0 1px #28a745;
        }

        input:checked + .slider:before {
            -webkit-transform: translateX(26px);
            -ms-transform: translateX(26px);
            transform: translateX(26px);
        }

        .slider.round {
            border-radius: 24px;
        }

        .slider.round:before {
            border-radius: 50%;
        }
    </style>
@endsection

@section('script')
    <script src="{{ asset('backend') }}/plugins/datatables/jquery.dataTables.min.js"></script>
    <script src="{{ asset('backend') }}/plugins/datatables-bs4/js/dataTables.bootstrap4.min.js"></script>
    <script src="{{ asset('backend') }}/plugins/datatables-responsive/js/dataTables.responsive.min.js"></script>
    <script>
        $(document).ready(function() {
            // Initialize DataTable for mobile-first approach
            $('#companiesTable').DataTable({
                responsive: true,
                paging: false,
                searching: false,
                info: false,
                autoWidth: false,
                columnDefs: [
                    { responsivePriority: 1, targets: 0 },
                    { responsivePriority: 2, targets: 5 },
                    { responsivePriority: 3, targets: 1 }
                ]
            });

            // Initialize tooltips
            $('[data-toggle="tooltip"]').tooltip();

            // Handle status switch
            $(document).on('change', '.status-switch', function() {
                var userId = $(this).data('id');
                var status = $(this).is(':checked') ? 1 : 0;
                var switchElement = $(this);
                var badge = $('#status_badge_' + userId);

                console.log('Status switch clicked:', {
                    userId: userId,
                    status: status,
                    switchElement: switchElement
                });

                // Disable switch temporarily
                switchElement.prop('disabled', true);

                $.ajax({
                    url: '{{ route('company.status.change') }}',
                    type: 'GET',
                    data: {
                        'status': status,
                        'id': userId,
                        '_token': '{{ csrf_token() }}'
                    },
                    beforeSend: function() {
                        console.log('Sending AJAX request to change status');
                    },
                    success: function(response) {
                        console.log('Success response:', response);

                        // Update badge
                        if (status == 1) {
                            badge.removeClass('badge-danger').addClass('badge-success').text('{{ __('activated') }}');
                        } else {
                            badge.removeClass('badge-success').addClass('badge-danger').text('{{ __('deactivated') }}');
                        }

                        // Show success message
                        Swal.fire({
                            icon: 'success',
                            title: 'Berhasil!',
                            text: 'Status perusahaan berhasil diubah.',
                            timer: 2000,
                            showConfirmButton: false
                        });

                        // Re-enable switch
                        switchElement.prop('disabled', false);
                    },
                    error: function(xhr, status, error) {
                        console.error('Error response:', xhr, status, error);

                        // Revert switch state
                        switchElement.prop('checked', !switchElement.is(':checked'));

                        // Show error message
                        Swal.fire({
                            icon: 'error',
                            title: 'Gagal!',
                            text: 'Terjadi kesalahan saat mengubah status perusahaan: ' + (xhr.responseJSON ? xhr.responseJSON.message : error),
                        });

                        // Re-enable switch
                        switchElement.prop('disabled', false);
                    }
                });
            });

            // Perbaikan aksesibilitas untuk modal
            $('.modal').on('hidden.bs.modal', function () {
                // Kembalikan fokus ke elemen yang membuka modal
                $(document.activeElement).blur();
                $('body').removeClass('modal-open');
                $('.modal-backdrop').remove();
            });

            // Simpan elemen yang membuka modal
            var lastFocusedElement;
            $('[data-toggle="modal"]').on('click', function() {
                lastFocusedElement = $(this);
            });

            // Kembalikan fokus saat modal ditutup
            $('.modal').on('hidden.bs.modal', function () {
                if (lastFocusedElement) {
                    lastFocusedElement.focus();
                }
            });

            // Handle document modal
            $('.view-document').on('click', function() {
                var companyId = $(this).data('company-id');
                var companyName = $(this).data('company-name');
                var documentUrl = $(this).data('document-url');
                var documentType = $(this).data('document-type');
                var status = $(this).data('status');
                var userId = $(this).data('user-id');

                $('#companyNameInModal').text(companyName);
                $('#downloadDocument').attr('href', documentUrl);

                // Clear previous content
                $('#documentViewer').empty();

                console.log('Document URL:', documentUrl);
                console.log('Document Type:', documentType);

                // Tambahkan informasi debugging
                $('#documentViewer').append('<div class="alert alert-info mt-2">' +
                    '<strong>Debug Info:</strong><br>' +
                    'URL Dokumen Asli: ' + documentUrl + '<br>' +
                    'Type: ' + documentType + '<br>' +
                    'User ID: ' + userId + '<br>' +
                    'Preview URL: ' + previewUrl + '<br>' +
                    'Download URL: ' + downloadUrl +
                '</div>');

                if (documentUrl) {
                    // Tampilkan tombol download
                    $('#downloadDocument').show();

                    // Deteksi jenis file dari URL jika document-type tidak tersedia
                    if (!documentType) {
                        var fileExtension = documentUrl.split('.').pop().toLowerCase();
                        documentType = fileExtension;
                        console.log('Detected extension:', fileExtension);
                    }

                    // Buat URL untuk preview dan download dokumen menggunakan controller baru
                    var previewUrl = '{{ route('admin.document.preview') }}?user_id=' + userId;
                    var downloadUrl = '{{ route('admin.document.download') }}?user_id=' + userId;

                    // Tambahkan tombol untuk membuka dokumen di tab baru
                    $('#documentViewer').append(
                        '<div class="text-center mb-3">' +
                        '<a href="' + previewUrl + '" class="btn btn-primary" target="_blank">' +
                        '<i class="fas fa-external-link-alt"></i> Buka Dokumen di Tab Baru' +
                        '</a>' +
                        '</div>'
                    );

                    // Update tombol download
                    $('#downloadDocument').attr('href', downloadUrl);

                    // Coba tampilkan preview berdasarkan jenis file
                    if (documentType.toLowerCase() === 'pdf') {
                        // Tampilkan pesan dan tombol untuk membuka PDF langsung
                        $('#documentViewer').append(
                            '<div class="pdf-container">' +
                            '<div class="alert alert-info">' +
                            '<i class="fas fa-file-pdf fa-2x mb-2"></i>' +
                            '<h5>Dokumen PDF</h5>' +
                            '<p>Untuk melihat dokumen PDF, silakan gunakan tombol di bawah ini:</p>' +
                            '<a href="' + previewUrl + '" class="btn btn-primary" target="_blank">' +
                            '<i class="fas fa-external-link-alt"></i> Buka PDF di Tab Baru' +
                            '</a>' +
                            '</div>' +
                            '</div>'
                        );
                    } else if (['jpg', 'jpeg', 'png'].includes(documentType.toLowerCase())) {
                        // Tampilkan gambar dengan fallback
                        $('#documentViewer').append(
                            '<div class="img-container">' +
                            '<h5>Preview Gambar:</h5>' +
                            '<img src="' + previewUrl + '" alt="Document" onerror="this.onerror=null; this.src=\'{{ asset("backend/image/default.png") }}\'; this.style.maxWidth=\'200px\'; this.parentNode.insertAdjacentHTML(\'afterend\', \'<p class=\\\'text-danger mt-2\\\'>Gambar tidak dapat dimuat.</p>\');">' +
                            '</div>'
                        );
                    } else {
                        $('#documentViewer').append('<p>Format dokumen tidak dapat ditampilkan untuk preview.</p>');
                    }
                } else {
                    $('#documentViewer').html('<p>Tidak ada dokumen yang diunggah.</p>');
                    $('#downloadDocument').hide();
                }

                // Set toggle button text and class based on status
                if (status == 1) {
                    $('#toggleStatusBtn').html('<i class="fas fa-power-off mr-1"></i> Nonaktifkan').removeClass('btn-success').addClass('btn-danger');
                } else {
                    $('#toggleStatusBtn').html('<i class="fas fa-power-off mr-1"></i> Aktifkan').removeClass('btn-danger').addClass('btn-success');
                }

                // Set up toggle button click handler
                $('#toggleStatusBtn').off('click').on('click', function() {
                    var newStatus = status == 1 ? 0 : 1;
                    var statusText = newStatus == 1 ? 'mengaktifkan' : 'menonaktifkan';
                    var companyName = $('#companyNameInModal').text();

                    // Konfirmasi terlebih dahulu
                    if (confirm('Apakah Anda yakin ingin ' + statusText + ' perusahaan "' + companyName + '"?')) {
                        // Tampilkan loading spinner
                        var btnText = $(this).text();
                        $(this).html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Loading...');
                        $(this).prop('disabled', true);

                        var btn = $(this);

                        console.log('Sending AJAX request to change status:', {
                            status: newStatus,
                            id: userId
                        });

                        // Gunakan AJAX dengan metode yang berbeda
                        $.ajax({
                            url: '{{ route('company.status.change') }}',
                            type: 'GET',
                            data: {
                                'status': newStatus,
                                'id': userId,
                                '_token': '{{ csrf_token() }}'
                            },
                            success: function(response) {
                                console.log('Success response:', response);

                                // Update status variable
                                status = newStatus;

                                // Update button text and class
                                if (status == 1) {
                                    btn.html('<i class="fas fa-power-off mr-1"></i> Nonaktifkan').removeClass('btn-success').addClass('btn-danger');
                                } else {
                                    btn.html('<i class="fas fa-power-off mr-1"></i> Aktifkan').removeClass('btn-danger').addClass('btn-success');
                                }

                                // Enable button
                                btn.prop('disabled', false);

                                // Update status badge in the table
                                var statusCell = $('button[data-user-id="' + userId + '"]').closest('td').find('.badge');
                                if (statusCell.length) {
                                    if (status == 1) {
                                        statusCell.removeClass('badge-danger').addClass('badge-success').text('{{ __('activated') }}');
                                    } else {
                                        statusCell.removeClass('badge-success').addClass('badge-danger').text('{{ __('deactivated') }}');
                                    }
                                }

                                // Tampilkan pesan sukses
                                alert('Status perusahaan berhasil diubah!');
                            },
                            error: function(xhr, status, error) {
                                console.error('Error response:', xhr.responseText);

                                // Tampilkan pesan error
                                alert('Terjadi kesalahan: ' + (xhr.responseJSON ? xhr.responseJSON.message : error));

                                // Kembalikan tombol ke keadaan semula
                                btn.prop('disabled', false);
                                btn.html(btnText);
                            }
                        });
                    }
                });
            });

            // Handle jobs modal
            $(document).on('click', '.view-jobs', function() {
                var companyId = $(this).data('company-id');
                var companyName = $(this).data('company-name');

                $('#companyNameInJobsModal').text(companyName);
                $('#jobsList').html('<div class="text-center p-5"><div class="spinner-border text-primary" role="status"><span class="sr-only">Loading...</span></div><p class="mt-2">Memuat data lowongan kerja...</p></div>');

                // Fetch jobs data
                $.ajax({
                    url: '{{ route('admin.company.jobs') }}',
                    type: 'GET',
                    data: {
                        company_id: companyId
                    },
                    success: function(response) {
                        console.log('Response:', response); // Debug

                        if (response && response.success && response.jobs && response.jobs.length > 0) {
                            var jobsHtml = '<div class="list-group">';

                            $.each(response.jobs, function(index, job) {
                                // Format deadline jika ada
                                var deadlineText = '';
                                if (job.deadline) {
                                    try {
                                        var deadlineDate = new Date(job.deadline);
                                        var formattedDeadline = deadlineDate.toLocaleDateString('id-ID', {
                                            day: 'numeric',
                                            month: 'long',
                                            year: 'numeric'
                                        });
                                        deadlineText = 'Deadline: ' + formattedDeadline;
                                    } catch (e) {
                                        deadlineText = 'Deadline: ' + job.deadline;
                                    }
                                } else {
                                    deadlineText = 'Tidak ada deadline';
                                }

                                jobsHtml += '<div class="list-group-item">';
                                jobsHtml += '<div class="d-flex w-100 justify-content-between">';
                                jobsHtml += '<h5 class="mb-1">' + job.title + '</h5>';
                                jobsHtml += '<small class="text-muted">' + deadlineText + '</small>';
                                jobsHtml += '</div>';

                                // Periksa apakah job_type dan job_type.name ada
                                var jobTypeName = job.job_type && job.job_type.name ? job.job_type.name : 'Tidak ada data';
                                var salaryText = job.salary_format ? job.salary_format : 'Gaji tidak ditentukan';

                                jobsHtml += '<p class="mb-1">' + jobTypeName + ' | ' + salaryText + '</p>';
                                jobsHtml += '<small class="text-muted">Pelamar: ' + job.applications_count + '</small>';
                                jobsHtml += '</div>';
                            });

                            jobsHtml += '</div>';
                            $('#jobsList').html(jobsHtml);
                        } else {
                            $('#jobsList').html('<div class="alert alert-info text-center"><i class="fas fa-info-circle mr-2"></i>Tidak ada lowongan kerja aktif untuk perusahaan ini.</div>');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Error:', xhr, status, error); // Debug
                        $('#jobsList').html('<div class="alert alert-danger text-center"><i class="fas fa-exclamation-triangle mr-2"></i>Gagal memuat data lowongan kerja: ' + (xhr.responseJSON ? xhr.responseJSON.message : error) + '</div>');
                    }
                });
            });

            // Handle delete confirmation
            $(document).on('click', '.delete-btn', function() {
                var companyId = $(this).data('company-id');
                var companyName = $(this).data('company-name');

                $('#companyNameInDeleteModal').text(companyName);
                $('#deleteForm').attr('action', '{{ route('company.destroy', '') }}/' + companyId);

                $('#deleteModal').modal('show');
            });

            // Handle filter form submission
            $('.select2bs4').on('change', function() {
                $('#filterButton').click();
            });

            // Reset filter button
            $('#resetFilterButton').on('click', function() {
                window.location.href = '{{ route('company.index') }}';
            });

            // Handle pagination links with AJAX
            $(document).on('click', '#paginationContainer a', function(e) {
                e.preventDefault();
                var url = $(this).attr('href');
                loadCompanies(url);
            });

            // Handle filter card clicks
            $('.stat-card').on('click', function() {
                var url = $(this).data('url');
                loadCompanies(url);

                // Update URL with filter parameters
                window.history.pushState({path: url}, '', url);

                // Highlight active card
                $('.stat-card').removeClass('active-card');
                $(this).addClass('active-card');
            });
        });

        // Function to apply filter with AJAX
        function applyFilter(form) {
            var url = $(form).attr('action');

            // Ambil parameter URL saat ini yang tidak ada di form
            var currentParams = {};
            var searchParams = new URLSearchParams(window.location.search);

            // Simpan parameter yang tidak ada di form
            for (var pair of searchParams.entries()) {
                var paramName = pair[0];
                var paramValue = pair[1];

                // Jika parameter tidak ada di form dan bukan 'page', simpan
                if (!$(form).find('[name="' + paramName + '"]').length && paramName !== 'page') {
                    currentParams[paramName] = paramValue;
                }
            }

            // Gabungkan dengan data form
            var formData = $(form).serialize();

            // Tambahkan parameter yang disimpan
            for (var key in currentParams) {
                if (formData.indexOf(key + '=') === -1) {
                    formData += '&' + key + '=' + currentParams[key];
                }
            }

            loadCompanies(url + '?' + formData);

            // Update URL with filter parameters
            var newUrl = window.location.origin + window.location.pathname + '?' + formData;
            window.history.pushState({path: newUrl}, '', newUrl);

            return false;
        }

        // Function to load companies with AJAX
        function loadCompanies(url) {
            // Show loading spinner
            $('#companiesTableContainer').html('<div class="text-center p-5"><div class="spinner-border text-primary" role="status"><span class="sr-only">Loading...</span></div><p class="mt-2">Memuat data...</p></div>');

            $.ajax({
                url: url,
                type: 'GET',
                dataType: 'html',
                success: function(response) {
                    // Extract the table container from the response
                    var tempDiv = document.createElement('div');
                    tempDiv.innerHTML = response;

                    var newTableContainer = $(tempDiv).find('#companiesTableContainer').html();
                    $('#companiesTableContainer').html(newTableContainer);

                    // Reinitialize DataTable
                    $('#companiesTable').DataTable({
                        responsive: true,
                        paging: false,
                        searching: false,
                        info: false,
                        autoWidth: false,
                        columnDefs: [
                            { responsivePriority: 1, targets: 0 },
                            { responsivePriority: 2, targets: 5 },
                            { responsivePriority: 3, targets: 1 }
                        ]
                    });

                    // Reinitialize tooltips
                    $('[data-toggle="tooltip"]').tooltip();

                    // Scroll to top of the table
                    $('html, body').animate({
                        scrollTop: $('#companiesTableContainer').offset().top - 100
                    }, 200);
                },
                error: function() {
                    $('#companiesTableContainer').html('<div class="alert alert-danger">Gagal memuat data. Silakan coba lagi.</div>');
                }
            });
        }
    </script>
@endsection
